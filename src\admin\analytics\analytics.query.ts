// All raw SQL queries for analytics module
export const QUERY_GRAPHS =
  'SELECT type, title, description, subtitle, row, column, api, method FROM graphs';
export const QUERY_GRAPH_FILTERS =
  'SELECT type, title, defaultValue, otherInfo FROM graphFilters';
export const QUERY_STAGE_COUNT = `
  SELECT stage, COUNT(*) AS count
  FROM public."registeredUsers"
  {{WHERE_CLAUSE}}

  GROUP BY stage;
`;

export const QUERY_REGISTRATION_SUMMARY = `
    SELECT
      COUNT(*) AS "registration_count",
      SUM(CASE WHEN "completedLoans" > 0 THEN 1 ELSE 0 END) AS "repeat_users",
      SUM(CASE WHEN "completedLoans" = 0 THEN 1 ELSE 0 END) AS "new_users",
      SUM(CASE WHEN gender = 'MALE' THEN 1 ELSE 0 END) AS "male_registration",
      SUM(CASE WHEN gender = 'FEMALE' THEN 1 ELSE 0 END) AS "female_registration",
      SUM(CASE WHEN "typeOfDevice" = '0' THEN 1 ELSE 0 END) AS "android_users",
      SUM(CASE WHEN "typeOfDevice" = '1' THEN 1 ELSE 0 END) AS "ios_users",
      SUM(CASE WHEN "typeOfDevice" = '2' THEN 1 ELSE 0 END) AS "web_users"
    FROM public."registeredUsers"
    {{WHERE_CLAUSE}}
  `;

export const CHART_GRAPH_DATE_QUERY = `
  SELECT * FROM {{TABLE_NAME}} WHERE graph_date = toDate('{{GRAPH_DATE}}');
`;

export const QUERY_SCORE_SUBQUERY_BASE = `
  SELECT "userId" FROM public."CibilScoreEntity"
  WHERE {{SCORE_CONDITIONS}}
`;

export const QUERY_AGE_SUBQUERY_BASE = `
  SELECT "userId" FROM public."KYCEntity"
  WHERE "aadhaarDOB" IS NOT NULL AND "aadhaarDOB" != ''
    AND (
      "aadhaarDOB" ~ '^\\\d{2}/\\\d{2}/\\\d{4}'
      OR "aadhaarDOB" ~ '^\\\d{2}-\\\d{2}-\\\d{4}'
      OR "aadhaarDOB" ~ '^\\\d{4}-\\\d{2}-\\\d{2}'
    )
    {{AGE_CONDITION_CLAUSE}}
`;

export const START_AGE_CONDITION = `
  EXTRACT(YEAR FROM age(
          CASE
            WHEN "aadhaarDOB" ~ '^\\\d{2}/\\\d{2}/\\\d{4}' THEN TO_DATE("aadhaarDOB", 'DD/MM/YYYY')
            WHEN "aadhaarDOB" ~ '^\\\d{2}-\\\d{2}-\\\d{4}' THEN TO_DATE("aadhaarDOB", 'DD-MM-YYYY')
            WHEN "aadhaarDOB" ~ '^\\\d{4}-\\\d{2}-\\\d{2}' THEN "aadhaarDOB"::DATE
            ELSE NULL
          END
        )) >=  {{START_AGE}}`;

export const END_AGE_CONDITION = `
  EXTRACT(YEAR FROM age(
          CASE
            WHEN "aadhaarDOB" ~ '^\\\d{2}/\\\d{2}/\\\d{4}' THEN TO_DATE("aadhaarDOB", 'DD/MM/YYYY')
            WHEN "aadhaarDOB" ~ '^\\\d{2}-\\\d{2}-\\\d{4}' THEN TO_DATE("aadhaarDOB", 'DD-MM-YYYY')
            WHEN "aadhaarDOB" ~ '^\\\d{4}-\\\d{2}-\\\d{2}' THEN "aadhaarDOB"::DATE
            ELSE NULL
          END
        )) <=  {{END_AGE}}`;
