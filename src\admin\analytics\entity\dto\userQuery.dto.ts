import {
  IsOptional,
  <PERSON><PERSON><PERSON><PERSON>tring,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';

export class UserQueryDto {
  @IsOptional()
  @IsDateString({}, { message: 'startDate must be a valid ISO date string' })
  startDate?: string;

  @IsOptional()
  @IsDateString({}, { message: 'endDate must be a valid ISO date string' })
  endDate?: string;

  @IsOptional()
  @IsString({ message: 'gender must be a string' })
  gender?: string;

  @IsOptional()
  @IsString({ message: 'state must be a string' })
  state?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'startCibil must be a number' })
  @Min(300)
  @Max(900)
  startCibil?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'endCibil must be a number' })
  @Min(300)
  @Max(900)
  endCibil?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'startPl must be a number' })
  @Min(300)
  @Max(900)
  startPl?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'endPl must be a number' })
  @Min(300)
  @Max(900)
  endPl?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'startAge must be a number' })
  startAge?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'endAge must be a number' })
  endAge?: number;

  @IsOptional()
  @Type(() => Boolean)
  flage?: boolean;

  // @IsOptional()
  // @Type(() => Number)
  // @IsNumber({}, { message: 'score must be a number' })
  // score?: number;
}
