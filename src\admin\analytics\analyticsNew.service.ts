import { Injectable } from '@nestjs/common';
import { fn, col, literal, Op } from 'sequelize';
import { ClickHouseService } from 'src/database/clickhouse/clickhouse.service';
import {
  // CHART_GRAPH_DATE_QUERY,
  QUERY_GRAPHS,
  QUERY_GRAPH_FILTERS,
} from './analytics.query';
import { UserStage, UserStageCount } from 'src/constant/objects';
import { CommonService } from 'src/utils/comman.service';
import { PgService } from 'src/database/pg/pg.service';
import { UserQueryDto } from './entity/dto/userQuery.dto';
import { RedisService } from 'src/database/redis/redis.service';
import { NUMBERS } from 'src/constant/objects';
@Injectable()
export class AnalyticsNewService {
  constructor(
    private readonly ClickHouseService: ClickHouseService,
    private readonly commonService: CommonService,
    private readonly pgService: PgService,
    private readonly redisService: RedisService,
  ) {}

  private redisKey(baseKey: string, query: UserQueryDto): string {
    const parts: string[] = [baseKey];
    if (query.gender) parts.push(`GENDER_${query.gender.toUpperCase()}`);
    if (query.state) parts.push(`STATE_${query.state.toUpperCase()}`);
    if (query.startAge || query.endAge)
      parts.push(`AGE_${query.startAge || ''}-${query.endAge || ''}`);
    if (query.startCibil || query.endCibil || query.startPl || query.endPl) {
      parts.push(
        `SCORE_CIBIL${query.startCibil || ''}-${query.endCibil || ''}_PL${query.startPl || ''}-${query.endPl || ''}`,
      );
    }
    return parts.join('_');
  }
  async getUserStageData(query) {
    const todayStr = new Date().toISOString().split('T')[0];
    const chDate = query?.startDate?.split('T')[0] || todayStr;
    const flag = !!query?.flag;
    const redisKey = this.redisKey('USER_STAGE_FILTER', query);

    // 2. Redis cache check (only for today & flag)
    if (flag && chDate === todayStr) {
      const cached = await this.redisService.getString(redisKey);
      console.log(' Redis cache:-->>>>>', redisKey, cached ? 'HIT' : 'MISS');
      if (cached) {
        return JSON.parse(cached);
      }
    }

    // 3. ClickHouse check (only for past date & flag)
    if (flag && chDate !== todayStr) {
      const chQuery = `
      SELECT * FROM UsersStageAnalytics WHERE graph_date = toDate('${chDate}');
    `;
      const chRows = await this.ClickHouseService.injectQuery(chQuery);
      console.log('ClickHouse query:-->>>>', chQuery);
      if (Array.isArray(chRows) && chRows.length) {
        console.log('ClickHouse data found for date:-->>>>', chDate);
        const row = chRows[0];
        return Object.keys(UserStage).map((stageKey) => ({
          xKey: this.commonService.toPascalCase(stageKey),
          yKey: Number(row[stageKey.toLowerCase()]) || 0,
        }));
      } else {
        console.log(' No ClickHouse data for date:-->>>>', chDate);
      }
    }

    // 4. Postgres fallback
    const result = await this.preStageRegUserCount(query);
    console.log('Postgres fallback result:--->>>>', result);

    // 5. Cache/Analytics update
    if (flag && chDate === todayStr) {
      await this.redisService.setString(
        redisKey,
        JSON.stringify(result),
        NUMBERS.THREE_HOURS_IN_SECONDS,
      );
      console.log('[UserStageData] Redis cache set:', redisKey, result.length);
    } else if (flag && chDate !== todayStr) {
      // Analytics (ClickHouse) ke liye format me daalo
      const rowObj = {
        graph_date: chDate,
        ...result.reduce((acc, row) => {
          acc[row.xKey.replace(/\s+/g, '_').toLowerCase()] = row.yKey;
          return acc;
        }, {}),
      };
      await this.ClickHouseService.insertToClickhouse(
        'UsersStageAnalytics',
        rowObj,
      );
      console.log(
        '[UserStageData] ClickHouse data inserted for date:',
        chDate,
        rowObj,
      );
    }

    return result;
  }

  // Fetch dashboard graphs and filters
  async getDashboard() {
    const [graphs, filters] = await Promise.all([
      this.ClickHouseService.injectQuery(QUERY_GRAPHS),
      this.ClickHouseService.injectQuery(QUERY_GRAPH_FILTERS),
    ]);

    const filtersArray = filters as any[];
    const today = new Date().toISOString().split('T')[0];
    const updatedFilters = filtersArray.map((filter) => {
      if (filter.type === 3) {
        return {
          ...filter,
          defaultValue: {
            start: today,
            end: today,
          },
        };
      }
      return filter;
    });
    return { filters: updatedFilters, graphs };
  }
  // Calculate age from DOB string (helper method)
  private calculateAge(dob: string): number | null {
    let year: number | null = null;
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(dob))
      year = parseInt(dob.split('/')[2], 10);
    else if (/^\d{2}-\d{2}-\d{4}$/.test(dob))
      year = parseInt(dob.split('-')[2], 10);
    else if (/^\d{4}-\d{2}-\d{2}$/.test(dob))
      year = parseInt(dob.split('-')[0], 10);
    if (!year) return null;
    return new Date().getFullYear() - year;
  }

  // Build Sequelize where conditions for user filtering
  private async buildUserIdFilters(
    query: UserQueryDto,
  ): Promise<string[] | null> {
    let userIdList: string[] | null = null;

    // Score range filtering
    if (query.startCibil || query.endCibil || query.startPl || query.endPl) {
      const scoreWhere: any = {};

      if (query.startCibil || query.endCibil) {
        scoreWhere.cibilScore = {};
        if (query.startCibil) scoreWhere.cibilScore[Op.gte] = query.startCibil;
        if (query.endCibil) scoreWhere.cibilScore[Op.lte] = query.endCibil;
      }

      if (query.startPl || query.endPl) {
        scoreWhere.plScore = {};
        if (query.startPl) scoreWhere.plScore[Op.gte] = query.startPl;
        if (query.endPl) scoreWhere.plScore[Op.lte] = query.endPl;
      }

      const scoreUsers = await this.pgService.findAll('CibilScoreEntity', {
        attributes: ['userId'],
        where: scoreWhere,
        raw: true,
      });

      if (!scoreUsers.length) return [];
      userIdList = scoreUsers.map((u) => u.userId);
    }

    // Age range filtering
    if (query.startAge || query.endAge) {
      const kycList = await this.pgService.findAll('KYCEntity', {
        attributes: ['userId', 'aadhaarDOB'],
        where: { aadhaarDOB: { [Op.ne]: null, [Op.not]: '' } },
        raw: true,
      });

      const filteredKyc = kycList.filter(({ aadhaarDOB }) => {
        const age = this.calculateAge(aadhaarDOB);
        if (age === null) return false;
        if (query.startAge && query.endAge)
          return age >= query.startAge && age <= query.endAge;
        if (query.startAge) return age >= query.startAge;
        if (query.endAge) return age <= query.endAge;
        return true;
      });

      const ageUserIds = filteredKyc.map((item) => item.userId);
      if (!ageUserIds.length) return [];

      // Intersect with existing userIdList if present
      if (userIdList) {
        userIdList = userIdList.filter((id) => ageUserIds.includes(id));
        if (!userIdList.length) return [];
      } else {
        userIdList = ageUserIds;
      }
    }

    return userIdList;
  }

  // Prepare user counts by stage with Sequelize-based filtering
  async preStageRegUserCount(query: UserQueryDto) {
    try {
      const { startDate, endDate } = this.commonService.getUTCDateRange(
        query?.startDate,
        query?.endDate,
      );

      // Build base where conditions
      const where: any = {};

      if (startDate && endDate) {
        where.createdAt = { [Op.gte]: startDate, [Op.lte]: endDate };
      }

      if (query?.gender) {
        where.gender = query.gender;
      }

      if (query?.state) {
        const states = Array.isArray(query.state)
          ? query.state.map((s) => s.trim().toUpperCase())
          : query.state.split(',').map((s) => s.trim().toUpperCase());
        where.state = states.length > 1 ? { [Op.in]: states } : states[0];
      }

      // Get filtered user IDs for age/score filters
      const userIdList = await this.buildUserIdFilters(query);
      if (userIdList !== null) {
        if (userIdList.length === 0) return []; // No users match filters
        where.id = { [Op.in]: userIdList };
      }

      // Execute Sequelize query to get stage counts
      const rows = await this.pgService.findAll('registeredUsers', {
        attributes: ['stage', [fn('COUNT', col('stage')), 'count']],
        where,
        group: ['stage'],
        raw: true,
      });

      // Initialize aggregate with all stages
      const aggregate = {};
      Object.keys(UserStage).forEach((key) => {
        aggregate[key] = 0;
      });

      // Populate aggregate with results
      rows.forEach((elem) => {
        const stageKey = Object.keys(UserStage).find(
          (k) => UserStage[k] === elem.stage,
        );
        if (stageKey) aggregate[stageKey] = parseInt(elem.count ?? '0', 10);
      });

      return Object.entries(aggregate).map(([stageKey, count]) => ({
        xKey: this.commonService.toPascalCase(stageKey),
        yKey: count,
      }));
    } catch (error) {
      console.error('Error in preStageRegUserCount:', {
        query,
        error,
      });
      throw error;
    }
  }

  // Registration summary with counts by user types and devices, etc.
  async getRegistrationSummary(query: UserQueryDto) {
    try {
      const todayStr = new Date().toISOString().split('T')[0];
      const flag = !!query?.flag;
      const redisKey = this.redisKey('REG_SUMMARY', query);

      // 1. Redis cache check
      if (flag && query?.startDate?.split('T')[0] === todayStr) {
        const cached = await this.redisService.getString(redisKey);
        if (cached) {
          console.log('RegistrationSummary from Redis cache');
          return JSON.parse(cached);
        }
      }

      // Build Sequelize where conditions
      const { startDate, endDate } = this.commonService.getUTCDateRange(
        query?.startDate,
        query?.endDate,
      );

      const where: any = {};

      if (startDate && endDate) {
        where.createdAt = { [Op.gte]: startDate, [Op.lte]: endDate };
      }

      if (query?.gender) {
        where.gender = query.gender;
      }

      if (query?.state) {
        const states = Array.isArray(query.state)
          ? query.state.map((s) => s.trim().toUpperCase())
          : query.state.split(',').map((s) => s.trim().toUpperCase());
        where.state = states.length > 1 ? { [Op.in]: states } : states[0];
      }

      // Get filtered user IDs for age/score filters
      const userIdList = await this.buildUserIdFilters(query);
      if (userIdList !== null) {
        if (userIdList.length === 0) return []; // No users match filters
        where.id = { [Op.in]: userIdList };
      }

      const result = await this.pgService.findAll('registeredUsers', {
        attributes: [
          [fn('COUNT', col('id')), 'registration_count'],
          [
            fn(
              'SUM',
              literal(`CASE WHEN "completedLoans" > 0 THEN 1 ELSE 0 END`),
            ),
            'repeat_users',
          ],
          [
            fn(
              'SUM',
              literal(`CASE WHEN "completedLoans" = 0 THEN 1 ELSE 0 END`),
            ),
            'new_users',
          ],
          [
            fn('SUM', literal(`CASE WHEN gender = 'MALE' THEN 1 ELSE 0 END`)),
            'male_registration',
          ],
          [
            fn('SUM', literal(`CASE WHEN gender = 'FEMALE' THEN 1 ELSE 0 END`)),
            'female_registration',
          ],
          [
            fn(
              'SUM',
              literal(`CASE WHEN "typeOfDevice" = '0' THEN 1 ELSE 0 END`),
            ),
            'android_users',
          ],
          [
            fn(
              'SUM',
              literal(`CASE WHEN "typeOfDevice" = '1' THEN 1 ELSE 0 END`),
            ),
            'ios_users',
          ],
          [
            fn(
              'SUM',
              literal(`CASE WHEN "typeOfDevice" = '2' THEN 1 ELSE 0 END`),
            ),
            'web_users',
          ],
        ],
        where,
        raw: true,
      });

      if (!result.length) return [];

      const summary = {};
      for (const key of Object.values(UserStageCount)) {
        summary[key] = result[0][key] ? Number(result[0][key]) : 0;
      }

      const formatted = Object.entries(summary).map(([xKey, yKey]) => ({
        xKey: this.commonService.toPascalCase(xKey),
        yKey,
      }));

      // 2. Redis cache if today
      if (
        flag &&
        query?.startDate?.split('T')[0] === todayStr &&
        formatted.length
      ) {
        console.log(
          'Setting Redis cache for RegistrationSummary',
          redisKey,
          formatted.length,
        );
        await this.redisService.setString(
          redisKey,
          JSON.stringify(formatted),
          NUMBERS.THREE_HOURS_IN_SECONDS,
        );
      }

      return formatted;
    } catch (error) {
      console.error('Error in getRegistrationSummary:', error);
      throw error;
    }
  }
}
